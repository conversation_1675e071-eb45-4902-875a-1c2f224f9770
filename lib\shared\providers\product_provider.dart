import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product_model.dart';
import '../../core/services/firebase_service.dart';
import '../../app.dart';

// Products stream provider - Enhanced with demo mode support
final productsProvider = StreamProvider<List<ProductModel>>((ref) {
  if (kDemoMode) {
    // Return sample products for demo mode
    return Stream.value(ProductService.getSampleProducts());
  }
  
  return FirebaseService.firestore
      .collection('products')
      .where('isAvailable', isEqualTo: true)
      .orderBy('createdAt', descending: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => ProductModel.fromFirestore(doc))
          .toList())
      .handleError((error) {
        if (kDebugMode) {
          print('Error loading products: $error');
        }
        return ProductService.getSampleProducts();
      });
});

// Single product provider - Enhanced with demo mode support
final productByIdProvider = StreamProvider.family<ProductModel?, String>((ref, productId) {
  if (kDemoMode) {
    // Find product in sample data
    final sampleProducts = ProductService.getSampleProducts();
    final product = sampleProducts.where((p) => p.id == productId).firstOrNull;
    return Stream.value(product);
  }
  
  return FirebaseService.firestore
      .collection('products')
      .doc(productId)
      .snapshots()
      .map((doc) => doc.exists ? ProductModel.fromFirestore(doc) : null)
      .handleError((error) {
        if (kDebugMode) {
          print('Error loading product $productId: $error');
        }
        // Return sample product as fallback
        final sampleProducts = ProductService.getSampleProducts();
        return sampleProducts.where((p) => p.id == productId).firstOrNull;
      });
});

// Popular products provider - Enhanced with demo mode support
final popularProductsProvider = StreamProvider<List<ProductModel>>((ref) {
  if (kDemoMode) {
    // Return top-rated sample products for demo mode
    final sampleProducts = ProductService.getSampleProducts();
    sampleProducts.sort((a, b) => b.rating.compareTo(a.rating));
    return Stream.value(sampleProducts.take(10).toList());
  }
  
  return FirebaseService.firestore
      .collection('products')
      .where('isAvailable', isEqualTo: true)
      .orderBy('rating', descending: true)
      .limit(10)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => ProductModel.fromFirestore(doc))
          .toList())
      .handleError((error) {
        if (kDebugMode) {
          print('Error loading popular products: $error');
        }
        final sampleProducts = ProductService.getSampleProducts();
        sampleProducts.sort((a, b) => b.rating.compareTo(a.rating));
        return sampleProducts.take(10).toList();
      });
});

// Products by category provider - Enhanced with demo mode support
final productsByCategoryProvider = StreamProvider.family<List<ProductModel>, String>((ref, category) {
  if (kDemoMode) {
    // Filter sample products by category for demo mode
    final sampleProducts = ProductService.getSampleProducts();
    return Stream.value(sampleProducts.where((p) => p.category == category).toList());
  }
  
  return FirebaseService.firestore
      .collection('products')
      .where('category', isEqualTo: category)
      .where('isAvailable', isEqualTo: true)
      .orderBy('createdAt', descending: true)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => ProductModel.fromFirestore(doc))
          .toList())
      .handleError((error) {
        if (kDebugMode) {
          print('Error loading products by category $category: $error');
        }
        final sampleProducts = ProductService.getSampleProducts();
        return sampleProducts.where((p) => p.category == category).toList();
      });
});

// Product search provider
final productSearchProvider = StateNotifierProvider<ProductSearchNotifier, AsyncValue<List<ProductModel>>>((ref) {
  return ProductSearchNotifier();
});

class ProductSearchNotifier extends StateNotifier<AsyncValue<List<ProductModel>>> {
  ProductSearchNotifier() : super(const AsyncValue.data([]));

  Future<void> searchProducts(String query) async {
    if (query.isEmpty) {
      state = const AsyncValue.data([]);
      return;
    }

    state = const AsyncValue.loading();
    
    try {
      if (kDemoMode) {
        // Use sample products for demo mode
        final sampleProducts = ProductService.getSampleProducts();
        final filteredProducts = sampleProducts.where((product) =>
            product.name.toLowerCase().contains(query.toLowerCase()) ||
            product.description.toLowerCase().contains(query.toLowerCase()) ||
            product.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase())))
        .toList();
        
        state = AsyncValue.data(filteredProducts);
        return;
      }
      
      final snapshot = await FirebaseService.firestore
          .collection('products')
          .where('isAvailable', isEqualTo: true)
          .get();

      final products = snapshot.docs
          .map((doc) => ProductModel.fromFirestore(doc))
          .where((product) =>
              product.name.toLowerCase().contains(query.toLowerCase()) ||
              product.description.toLowerCase().contains(query.toLowerCase()) ||
              product.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase())))
          .toList();

      state = AsyncValue.data(products);
    } catch (e) {
      if (kDebugMode) {
        print('Search error: $e');
      }
      // Fallback to sample products on error
      final sampleProducts = ProductService.getSampleProducts();
      final filteredProducts = sampleProducts.where((product) =>
          product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.description.toLowerCase().contains(query.toLowerCase()) ||
          product.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase())))
      .toList();
      
      state = AsyncValue.data(filteredProducts);
    }
  }

  void clearSearch() {
    state = const AsyncValue.data([]);
  }
}

// Product service provider
final productServiceProvider = Provider<ProductService>((ref) {
  return ProductService();
});

class ProductService {
  // Get sample products for development
  static List<ProductModel> getSampleProducts() {
    final now = DateTime.now();
    return [
      ProductModel(
        id: 'product1',
        name: 'Bag',
        description: 'Beautiful bag made from recycled materials',
        price: 500,
        category: 'Accessories',
        imageUrls: ['assets/images/bagpack.png'],
        sellerId: 'seller1',
        sellerName: 'EcoCraft Store',
        stockQuantity: 10,
        rating: 4.5,
        reviewCount: 23,
        tags: ['bag', 'recycled', 'eco-friendly'],
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now,
        stats: ProductStats(views: 156, orders: 23, revenue: 11500, favorites: 45),
      ),
      ProductModel(
        id: 'product2',
        name: 'Wooden Planter',
        description: 'Handcrafted planter from reclaimed wood',
        price: 750,
        category: 'Home Decor',
        imageUrls: ['assets/images/plantstand.png'],
        sellerId: 'seller2',
        sellerName: 'Green Crafters',
        stockQuantity: 5,
        rating: 4.8,
        reviewCount: 12,
        tags: ['planter', 'wood', 'garden'],
        createdAt: now.subtract(const Duration(days: 3)),
        updatedAt: now,
        stats: ProductStats(views: 89, orders: 12, revenue: 9000, favorites: 28),
      ),
      ProductModel(
        id: 'product3',
        name: 'Bird Feeder',
        description: 'Eco-friendly bird feeder from plastic bottles',
        price: 299,
        category: 'Home Decor',
        imageUrls: ['assets/images/bird-feeder.png'],
        sellerId: 'seller1',
        sellerName: 'EcoCraft Store',
        stockQuantity: 15,
        rating: 4.2,
        reviewCount: 34,
        tags: ['bird feeder', 'plastic', 'garden'],
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now,
        stats: ProductStats(views: 234, orders: 34, revenue: 10166, favorites: 67),
      ),
      ProductModel(
        id: 'product4',
        name: 'Desk Organizer',
        description: 'Multi-compartment desk organizer from cardboard',
        price: 450,
        category: 'Home Decor',
        imageUrls: ['assets/images/deskorganizer.png'],
        sellerId: 'seller3',
        sellerName: 'Upcycle Masters',
        stockQuantity: 8,
        rating: 4.6,
        reviewCount: 18,
        tags: ['organizer', 'desk', 'cardboard'],
        createdAt: now.subtract(const Duration(hours: 12)),
        updatedAt: now,
        stats: ProductStats(views: 123, orders: 18, revenue: 8100, favorites: 32),
      ),
      ProductModel(
        id: 'product5',
        name: 'Shoes',
        description: 'Comfortable shoes made from recycled materials',
        price: 1500,
        category: 'Footwear',
        imageUrls: ['assets/images/boy.png'],
        sellerId: 'seller1',
        sellerName: 'EcoCraft Store',
        stockQuantity: 12,
        rating: 4.3,
        reviewCount: 28,
        tags: ['shoes', 'recycled', 'comfort'],
        createdAt: now.subtract(const Duration(hours: 8)),
        updatedAt: now,
        stats: ProductStats(views: 89, orders: 15, revenue: 22500, favorites: 22),
      ),
      ProductModel(
        id: 'product6',
        name: 'T-Shirt',
        description: 'Eco-friendly t-shirt from organic cotton',
        price: 700,
        category: 'Clothing',
        imageUrls: ['assets/images/boy.png'],
        sellerId: 'seller2',
        sellerName: 'Green Crafters',
        stockQuantity: 20,
        rating: 4.7,
        reviewCount: 45,
        tags: ['t-shirt', 'organic', 'cotton'],
        createdAt: now.subtract(const Duration(hours: 4)),
        updatedAt: now,
        stats: ProductStats(views: 167, orders: 32, revenue: 22400, favorites: 38),
      ),
      ProductModel(
        id: 'product7',
        name: 'Watch',
        description: 'Stylish watch with recycled metal components',
        price: 2000,
        category: 'Accessories',
        imageUrls: ['assets/images/boy.png'],
        sellerId: 'seller3',
        sellerName: 'Upcycle Masters',
        stockQuantity: 6,
        rating: 4.9,
        reviewCount: 15,
        tags: ['watch', 'metal', 'stylish'],
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now,
        stats: ProductStats(views: 78, orders: 8, revenue: 16000, favorites: 19),
      ),
    ];
  }

  // Add product to favorites
  Future<void> addToFavorites(String productId, String userId) async {
    try {
      await FirebaseService.firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(productId)
          .set({
        'productId': productId,
        'addedAt': Timestamp.now(),
      });

      // Update product favorites count
      await FirebaseService.firestore
          .collection('products')
          .doc(productId)
          .update({
        'stats.favorites': FieldValue.increment(1),
      });
    } catch (e) {
      throw Exception('Failed to add to favorites: $e');
    }
  }

  // Remove product from favorites
  Future<void> removeFromFavorites(String productId, String userId) async {
    try {
      await FirebaseService.firestore
          .collection('users')
          .doc(userId)
          .collection('favorites')
          .doc(productId)
          .delete();

      // Update product favorites count
      await FirebaseService.firestore
          .collection('products')
          .doc(productId)
          .update({
        'stats.favorites': FieldValue.increment(-1),
      });
    } catch (e) {
      throw Exception('Failed to remove from favorites: $e');
    }
  }

  // Update product views - skip in demo mode
  Future<void> updateProductViews(String productId) async {
    if (kDemoMode) return; // Skip Firebase calls in demo mode
    
    try {
      await FirebaseService.firestore
          .collection('products')
          .doc(productId)
          .update({
        'stats.views': FieldValue.increment(1),
      });
    } catch (e) {
      // Silently fail for view updates
      if (kDebugMode) {
        print('Failed to update product views: $e');
      }
    }
  }

  // Create new product - skip in demo mode
  Future<void> createProduct(ProductModel product) async {
    if (kDemoMode) {
      // In demo mode, just simulate success
      await Future.delayed(const Duration(milliseconds: 500));
      return;
    }
    
    try {
      await FirebaseService.firestore
          .collection('products')
          .doc(product.id)
          .set(product.toFirestore());
    } catch (e) {
      throw Exception('Failed to create product: $e');
    }
  }

  // Update product - skip in demo mode
  Future<void> updateProduct(ProductModel product) async {
    if (kDemoMode) {
      // In demo mode, just simulate success
      await Future.delayed(const Duration(milliseconds: 500));
      return;
    }
    
    try {
      await FirebaseService.firestore
          .collection('products')
          .doc(product.id)
          .update(product.toFirestore());
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }

  // Delete product - skip in demo mode
  Future<void> deleteProduct(String productId) async {
    if (kDemoMode) {
      // In demo mode, just simulate success
      await Future.delayed(const Duration(milliseconds: 500));
      return;
    }
    
    try {
      await FirebaseService.firestore
          .collection('products')
          .doc(productId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }
}
