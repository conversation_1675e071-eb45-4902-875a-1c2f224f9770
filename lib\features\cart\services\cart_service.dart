import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../../core/services/firebase_service.dart';
import '../../../app.dart';
import '../models/cart_item.dart';
import '../../../shared/models/product_model.dart';

class CartService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;
  
  // In-memory storage for demo mode
  static final Map<String, Cart> _demoCarts = {};
  
  // Stream controller for demo mode
  static final StreamController<Cart> _demoCartController = 
      StreamController<Cart>.broadcast();

  /// Add item to cart
  static Future<void> addToCart(String userId, ProductModel product, {int quantity = 1}) async {
    if (kDemoMode) {
      // In demo mode, add to in-memory storage
      final currentCart = _demoCarts[userId] ?? Cart.empty();
      
      final cartItem = CartItem(
        id: 'demo_${DateTime.now().millisecondsSinceEpoch}',
        productId: product.id,
        productName: product.name,
        productDescription: product.description,
        price: product.price,
        imageUrl: product.imageUrls.isNotEmpty ? product.imageUrls.first : null,
        quantity: quantity,
        sellerId: product.sellerId,
        sellerName: product.sellerName,
        addedAt: DateTime.now(),
      );
      
      final updatedCart = currentCart.addItem(cartItem);
      _demoCarts[userId] = updatedCart;
      
      // Notify listeners
      _demoCartController.add(updatedCart);
      
      if (kDebugMode) {
        print('✅ Demo: Added ${product.name} to cart (quantity: $quantity)');
      }
      return;
    }
    
    try {
      final cartItem = CartItem(
        id: '', // Will be set by Firestore
        productId: product.id,
        productName: product.name,
        productDescription: product.description,
        price: product.price,
        imageUrl: product.imageUrls.isNotEmpty ? product.imageUrls.first : null,
        quantity: quantity,
        sellerId: product.sellerId,
        sellerName: product.sellerName,
        addedAt: DateTime.now(),
      );
      
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('cart')
          .add(cartItem.toFirestore());
      
      if (kDebugMode) {
        print('✅ Added ${product.name} to cart');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding to cart: $e');
      }
      throw Exception('Failed to add item to cart: $e');
    }
  }

  /// Remove item from cart
  static Future<void> removeFromCart(String userId, String productId) async {
    if (kDemoMode) {
      // In demo mode, remove from in-memory storage
      final currentCart = _demoCarts[userId] ?? Cart.empty();
      final updatedCart = currentCart.removeItem(productId);
      _demoCarts[userId] = updatedCart;
      
      // Notify listeners
      _demoCartController.add(updatedCart);
      
      if (kDebugMode) {
        print('✅ Demo: Removed item from cart');
      }
      return;
    }
    
    try {
      final cartQuery = await _firestore
          .collection('users')
          .doc(userId)
          .collection('cart')
          .where('productId', isEqualTo: productId)
          .get();
      
      for (final doc in cartQuery.docs) {
        await doc.reference.delete();
      }
      
      if (kDebugMode) {
        print('✅ Removed item from cart');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error removing from cart: $e');
      }
      throw Exception('Failed to remove item from cart: $e');
    }
  }

  /// Update item quantity in cart
  static Future<void> updateQuantity(String userId, String productId, int quantity) async {
    if (quantity <= 0) {
      return removeFromCart(userId, productId);
    }
    
    if (kDemoMode) {
      // In demo mode, update in-memory storage
      final currentCart = _demoCarts[userId] ?? Cart.empty();
      final updatedCart = currentCart.updateItemQuantity(productId, quantity);
      _demoCarts[userId] = updatedCart;
      
      // Notify listeners
      _demoCartController.add(updatedCart);
      
      if (kDebugMode) {
        print('✅ Demo: Updated cart item quantity to $quantity');
      }
      return;
    }
    
    try {
      final cartQuery = await _firestore
          .collection('users')
          .doc(userId)
          .collection('cart')
          .where('productId', isEqualTo: productId)
          .get();
      
      for (final doc in cartQuery.docs) {
        await doc.reference.update({'quantity': quantity});
      }
      
      if (kDebugMode) {
        print('✅ Updated cart item quantity');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating cart quantity: $e');
      }
      throw Exception('Failed to update cart quantity: $e');
    }
  }

  /// Clear entire cart
  static Future<void> clearCart(String userId) async {
    if (kDemoMode) {
      // In demo mode, clear in-memory storage
      _demoCarts[userId] = Cart.empty();
      
      // Notify listeners
      _demoCartController.add(Cart.empty());
      
      if (kDebugMode) {
        print('✅ Demo: Cleared cart');
      }
      return;
    }
    
    try {
      final cartQuery = await _firestore
          .collection('users')
          .doc(userId)
          .collection('cart')
          .get();
      
      for (final doc in cartQuery.docs) {
        await doc.reference.delete();
      }
      
      if (kDebugMode) {
        print('✅ Cleared cart');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing cart: $e');
      }
      throw Exception('Failed to clear cart: $e');
    }
  }

  /// Get user's cart
  static Stream<Cart> getCart(String userId) {
    if (kDemoMode) {
      // In demo mode, return reactive stream of in-memory cart
      return Stream.multi((controller) {
        // Add current cart immediately
        final currentCart = _demoCarts[userId] ?? Cart.empty();
        controller.add(currentCart);
        
        // Listen for updates
        final subscription = _demoCartController.stream.listen((cart) {
          controller.add(cart);
        });
        
        controller.onCancel = () {
          subscription.cancel();
        };
      });
    }
    
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('cart')
        .orderBy('addedAt', descending: true)
        .snapshots()
        .map((snapshot) {
          final items = snapshot.docs
              .map((doc) => CartItem.fromFirestore(doc))
              .toList();
          return Cart(items: items, updatedAt: DateTime.now());
        });
  }

  /// Get cart item count
  static Future<int> getCartItemCount(String userId) async {
    if (kDemoMode) {
      final cart = _demoCarts[userId] ?? Cart.empty();
      return cart.totalItems;
    }
    
    try {
      final cartQuery = await _firestore
          .collection('users')
          .doc(userId)
          .collection('cart')
          .get();
      
      int totalItems = 0;
      for (final doc in cartQuery.docs) {
        final data = doc.data();
        totalItems += (data['quantity'] ?? 1) as int;
      }
      
      return totalItems;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting cart count: $e');
      }
      return 0;
    }
  }
}
