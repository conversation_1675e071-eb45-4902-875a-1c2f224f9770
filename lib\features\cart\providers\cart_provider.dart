import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../models/cart_item.dart';
import '../services/cart_service.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../shared/models/product_model.dart';

// Cart stream provider
final cartProvider = StreamProvider.family<Cart, String>((ref, userId) {
  return CartService.getCart(userId);
});

// Current user's cart provider
final currentUserCartProvider = StreamProvider<Cart>((ref) {
  final currentUserAsync = ref.watch(currentUserProvider);
  
  return currentUserAsync.when(
    data: (user) {
      if (user == null) {
        return Stream.value(Cart.empty());
      }
      return CartService.getCart(user.id);
    },
    loading: () => Stream.value(Cart.empty()),
    error: (_, __) => Stream.value(Cart.empty()),
  );
});

// Cart item count provider
final cartItemCountProvider = FutureProvider<int>((ref) async {
  final currentUserAsync = ref.watch(currentUserProvider);
  
  return currentUserAsync.when(
    data: (user) async {
      if (user == null) return 0;
      return await CartService.getCartItemCount(user.id);
    },
    loading: () => 0,
    error: (_, __) => 0,
  );
});

// Cart notifier for managing cart operations
final cartNotifierProvider = StateNotifierProvider<CartNotifier, CartState>((ref) {
  return CartNotifier(ref);
});

class CartState {
  final bool isLoading;
  final String? error;
  final String? successMessage;

  CartState({
    this.isLoading = false,
    this.error,
    this.successMessage,
  });

  CartState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
  }) {
    return CartState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
    );
  }
}

class CartNotifier extends StateNotifier<CartState> {
  final Ref _ref;

  CartNotifier(this._ref) : super(CartState());

  /// Add product to cart
  Future<bool> addToCart(ProductModel product, {int quantity = 1}) async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) {
      state = state.copyWith(error: 'User not authenticated');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      await CartService.addToCart(currentUser.id, product, quantity: quantity);
      
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Added ${product.name} to cart',
      );

      // Refresh cart providers
      _ref.invalidate(cartItemCountProvider);

      if (kDebugMode) {
        print('✅ Added ${product.name} to cart');
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to add item to cart: $e',
      );

      if (kDebugMode) {
        print('❌ Error adding to cart: $e');
      }

      return false;
    }
  }

  /// Remove item from cart
  Future<bool> removeFromCart(String productId) async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) {
      state = state.copyWith(error: 'User not authenticated');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      await CartService.removeFromCart(currentUser.id, productId);
      
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Item removed from cart',
      );

      // Refresh cart providers
      _ref.invalidate(cartItemCountProvider);

      if (kDebugMode) {
        print('✅ Removed item from cart');
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to remove item from cart: $e',
      );

      if (kDebugMode) {
        print('❌ Error removing from cart: $e');
      }

      return false;
    }
  }

  /// Update item quantity
  Future<bool> updateQuantity(String productId, int quantity) async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) {
      state = state.copyWith(error: 'User not authenticated');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      await CartService.updateQuantity(currentUser.id, productId, quantity);
      
      state = state.copyWith(
        isLoading: false,
        successMessage: quantity > 0 ? 'Quantity updated' : 'Item removed from cart',
      );

      // Refresh cart providers
      _ref.invalidate(cartItemCountProvider);

      if (kDebugMode) {
        print('✅ Updated cart item quantity');
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update quantity: $e',
      );

      if (kDebugMode) {
        print('❌ Error updating quantity: $e');
      }

      return false;
    }
  }

  /// Clear entire cart
  Future<bool> clearCart() async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) {
      state = state.copyWith(error: 'User not authenticated');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      await CartService.clearCart(currentUser.id);
      
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Cart cleared',
      );

      // Refresh cart providers
      _ref.invalidate(cartItemCountProvider);

      if (kDebugMode) {
        print('✅ Cart cleared');
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to clear cart: $e',
      );

      if (kDebugMode) {
        print('❌ Error clearing cart: $e');
      }

      return false;
    }
  }

  /// Clear state messages
  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }
}
