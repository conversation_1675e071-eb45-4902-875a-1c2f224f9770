import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../../../core/services/firebase_service.dart';
import '../models/community_post.dart';

class CommunityService {
  static final FirebaseFirestore _firestore = FirebaseService.firestore;

  /// Create a new community post
  static Future<String> createPost(CommunityPost post) async {
    try {
      final docRef = await _firestore.collection('community_posts').add(post.toFirestore());
      
      if (kDebugMode) {
        print('✅ Community post created: ${docRef.id}');
      }
      
      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating community post: $e');
      }
      throw Exception('Failed to create post: $e');
    }
  }

  /// Get all community posts
  static Stream<List<CommunityPost>> getPosts() {
    return _firestore
        .collection('community_posts')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => CommunityPost.fromFirestore(doc))
            .toList());
  }

  /// Get posts by user
  static Stream<List<CommunityPost>> getPostsByUser(String userId) {
    return _firestore
        .collection('community_posts')
        .where('authorId', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => CommunityPost.fromFirestore(doc))
            .toList());
  }

  /// Update a post
  static Future<void> updatePost(String postId, Map<String, dynamic> updates) async {
    try {
      await _firestore.collection('community_posts').doc(postId).update({
        ...updates,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      if (kDebugMode) {
        print('✅ Community post updated: $postId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating community post: $e');
      }
      throw Exception('Failed to update post: $e');
    }
  }

  /// Delete a post
  static Future<void> deletePost(String postId) async {
    try {
      await _firestore.collection('community_posts').doc(postId).delete();
      
      if (kDebugMode) {
        print('✅ Community post deleted: $postId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting community post: $e');
      }
      throw Exception('Failed to delete post: $e');
    }
  }

  /// Like/unlike a post
  static Future<void> toggleLike(String postId, String userId) async {
    try {
      final postRef = _firestore.collection('community_posts').doc(postId);
      
      await _firestore.runTransaction((transaction) async {
        final postDoc = await transaction.get(postRef);
        
        if (!postDoc.exists) {
          throw Exception('Post not found');
        }
        
        final data = postDoc.data()!;
        final likes = List<String>.from(data['likes'] ?? []);
        
        if (likes.contains(userId)) {
          likes.remove(userId);
        } else {
          likes.add(userId);
        }
        
        transaction.update(postRef, {
          'likes': likes,
          'likeCount': likes.length,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });
      
      if (kDebugMode) {
        print('✅ Post like toggled: $postId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error toggling like: $e');
      }
      throw Exception('Failed to toggle like: $e');
    }
  }

  /// Add comment to a post
  static Future<void> addComment(String postId, String userId, String content) async {
    try {
      final commentData = {
        'authorId': userId,
        'content': content,
        'createdAt': FieldValue.serverTimestamp(),
      };
      
      await _firestore
          .collection('community_posts')
          .doc(postId)
          .collection('comments')
          .add(commentData);
      
      // Update comment count
      await _firestore.collection('community_posts').doc(postId).update({
        'commentCount': FieldValue.increment(1),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      if (kDebugMode) {
        print('✅ Comment added to post: $postId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error adding comment: $e');
      }
      throw Exception('Failed to add comment: $e');
    }
  }

  /// Get comments for a post
  static Stream<List<Map<String, dynamic>>> getComments(String postId) {
    return _firestore
        .collection('community_posts')
        .doc(postId)
        .collection('comments')
        .orderBy('createdAt', descending: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => {
                  'id': doc.id,
                  ...doc.data(),
                })
            .toList());
  }
}
