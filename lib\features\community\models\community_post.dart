import 'package:cloud_firestore/cloud_firestore.dart';

class CommunityPost {
  final String id;
  final String authorId;
  final String authorName;
  final String? authorAvatar;
  final String content;
  final List<String> imageUrls;
  final List<String> tags;
  final List<String> likes;
  final int likeCount;
  final int commentCount;
  final int shareCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  final PostType type;
  final Map<String, dynamic>? metadata;

  CommunityPost({
    required this.id,
    required this.authorId,
    required this.authorName,
    this.authorAvatar,
    required this.content,
    this.imageUrls = const [],
    this.tags = const [],
    this.likes = const [],
    this.likeCount = 0,
    this.commentCount = 0,
    this.shareCount = 0,
    required this.createdAt,
    required this.updatedAt,
    this.type = PostType.general,
    this.metadata,
  });

  factory CommunityPost.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CommunityPost(
      id: doc.id,
      authorId: data['authorId'] ?? '',
      authorName: data['authorName'] ?? '',
      authorAvatar: data['authorAvatar'],
      content: data['content'] ?? '',
      imageUrls: List<String>.from(data['imageUrls'] ?? []),
      tags: List<String>.from(data['tags'] ?? []),
      likes: List<String>.from(data['likes'] ?? []),
      likeCount: data['likeCount'] ?? 0,
      commentCount: data['commentCount'] ?? 0,
      shareCount: data['shareCount'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      type: PostType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => PostType.general,
      ),
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'authorId': authorId,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'content': content,
      'imageUrls': imageUrls,
      'tags': tags,
      'likes': likes,
      'likeCount': likeCount,
      'commentCount': commentCount,
      'shareCount': shareCount,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'type': type.name,
      'metadata': metadata,
    };
  }

  CommunityPost copyWith({
    String? id,
    String? authorId,
    String? authorName,
    String? authorAvatar,
    String? content,
    List<String>? imageUrls,
    List<String>? tags,
    List<String>? likes,
    int? likeCount,
    int? commentCount,
    int? shareCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    PostType? type,
    Map<String, dynamic>? metadata,
  }) {
    return CommunityPost(
      id: id ?? this.id,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      content: content ?? this.content,
      imageUrls: imageUrls ?? this.imageUrls,
      tags: tags ?? this.tags,
      likes: likes ?? this.likes,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      shareCount: shareCount ?? this.shareCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
    );
  }

  bool isLikedBy(String userId) => likes.contains(userId);

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  @override
  String toString() {
    return 'CommunityPost(id: $id, author: $authorName, content: ${content.substring(0, content.length > 50 ? 50 : content.length)}...)';
  }
}

enum PostType {
  general,
  upcycling,
  tutorial,
  showcase,
  question,
  tip,
  challenge;

  String get displayName {
    switch (this) {
      case PostType.general:
        return 'General';
      case PostType.upcycling:
        return 'Upcycling Project';
      case PostType.tutorial:
        return 'Tutorial';
      case PostType.showcase:
        return 'Showcase';
      case PostType.question:
        return 'Question';
      case PostType.tip:
        return 'Tip';
      case PostType.challenge:
        return 'Challenge';
    }
  }

  String get emoji {
    switch (this) {
      case PostType.general:
        return '💬';
      case PostType.upcycling:
        return '♻️';
      case PostType.tutorial:
        return '📖';
      case PostType.showcase:
        return '🎨';
      case PostType.question:
        return '❓';
      case PostType.tip:
        return '💡';
      case PostType.challenge:
        return '🏆';
    }
  }
}
