import 'package:cloud_firestore/cloud_firestore.dart';

class CartItem {
  final String id;
  final String productId;
  final String productName;
  final String productDescription;
  final double price;
  final String? imageUrl;
  final int quantity;
  final String sellerId;
  final String sellerName;
  final DateTime addedAt;
  final Map<String, dynamic>? metadata;

  CartItem({
    required this.id,
    required this.productId,
    required this.productName,
    required this.productDescription,
    required this.price,
    this.imageUrl,
    required this.quantity,
    required this.sellerId,
    required this.sellerName,
    required this.addedAt,
    this.metadata,
  });

  factory CartItem.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return CartItem(
      id: doc.id,
      productId: data['productId'] ?? '',
      productName: data['productName'] ?? '',
      productDescription: data['productDescription'] ?? '',
      price: (data['price'] ?? 0).toDouble(),
      imageUrl: data['imageUrl'],
      quantity: data['quantity'] ?? 1,
      sellerId: data['sellerId'] ?? '',
      sellerName: data['sellerName'] ?? '',
      addedAt: (data['addedAt'] as Timestamp).toDate(),
      metadata: data['metadata'],
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'productId': productId,
      'productName': productName,
      'productDescription': productDescription,
      'price': price,
      'imageUrl': imageUrl,
      'quantity': quantity,
      'sellerId': sellerId,
      'sellerName': sellerName,
      'addedAt': Timestamp.fromDate(addedAt),
      'metadata': metadata,
    };
  }

  CartItem copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productDescription,
    double? price,
    String? imageUrl,
    int? quantity,
    String? sellerId,
    String? sellerName,
    DateTime? addedAt,
    Map<String, dynamic>? metadata,
  }) {
    return CartItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productDescription: productDescription ?? this.productDescription,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      quantity: quantity ?? this.quantity,
      sellerId: sellerId ?? this.sellerId,
      sellerName: sellerName ?? this.sellerName,
      addedAt: addedAt ?? this.addedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  double get totalPrice => price * quantity;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CartItem && other.productId == productId;
  }

  @override
  int get hashCode => productId.hashCode;
}

class Cart {
  final List<CartItem> items;
  final DateTime updatedAt;

  Cart({
    required this.items,
    required this.updatedAt,
  });

  Cart.empty() : items = [], updatedAt = DateTime.now();

  double get totalAmount => items.fold(0.0, (sum, item) => sum + item.totalPrice);
  
  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);
  
  bool get isEmpty => items.isEmpty;
  
  bool get isNotEmpty => items.isNotEmpty;

  Cart copyWith({
    List<CartItem>? items,
    DateTime? updatedAt,
  }) {
    return Cart(
      items: items ?? this.items,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Cart addItem(CartItem item) {
    final existingIndex = items.indexWhere((i) => i.productId == item.productId);
    
    if (existingIndex >= 0) {
      // Update quantity if item already exists
      final updatedItems = List<CartItem>.from(items);
      updatedItems[existingIndex] = updatedItems[existingIndex].copyWith(
        quantity: updatedItems[existingIndex].quantity + item.quantity,
      );
      return copyWith(items: updatedItems, updatedAt: DateTime.now());
    } else {
      // Add new item
      return copyWith(
        items: [...items, item],
        updatedAt: DateTime.now(),
      );
    }
  }

  Cart removeItem(String productId) {
    return copyWith(
      items: items.where((item) => item.productId != productId).toList(),
      updatedAt: DateTime.now(),
    );
  }

  Cart updateItemQuantity(String productId, int quantity) {
    if (quantity <= 0) {
      return removeItem(productId);
    }

    final updatedItems = items.map((item) {
      if (item.productId == productId) {
        return item.copyWith(quantity: quantity);
      }
      return item;
    }).toList();

    return copyWith(items: updatedItems, updatedAt: DateTime.now());
  }

  Cart clearCart() {
    return Cart.empty();
  }
}
