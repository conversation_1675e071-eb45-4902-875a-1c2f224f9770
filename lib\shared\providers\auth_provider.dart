import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../../core/services/firebase_service.dart';
import '../../app.dart';
import 'mock_auth_provider.dart';

// Demo mode logout state
final _demoLoggedOutProvider = StateProvider<bool>((ref) => false);

// Demo mode reset function - call this when app starts
void resetDemoMode(WidgetRef ref) {
  if (kDemoMode) {
    ref.read(_demoLoggedOutProvider.notifier).state = false;
    if (kDebugMode) {
      print('🔧 Demo Mode: Reset logout state on app start');
    }
  }
}

// Auth state provider - Enhanced with better error handling
final authStateProvider = StreamProvider<User?>((ref) {
  if (kDemoMode) {
    final isLoggedOut = ref.watch(_demoLoggedOutProvider);
    if (isLoggedOut) {
      return Stream.value(null);
    }
    return Stream.value(MockUser());
  }

  return FirebaseAuth.instance.authStateChanges().handleError((error) {
    if (kDebugMode) {
      print('Auth state error: $error');
    }
    return null;
  });
});

// Current user provider - Enhanced with proper demo mode isolation
final currentUserProvider = StreamProvider<UserModel?>((ref) {
  if (kDemoMode) {
    final isLoggedOut = ref.watch(_demoLoggedOutProvider);
    if (isLoggedOut) {
      return Stream.value(null);
    }
    
    // Demo Mode: Uses local mock data instead of Firebase
    // This prevents "Missing or insufficient permissions" errors
    // To switch to real Firebase, set kDemoMode = false in app.dart
    if (kDebugMode) {
      print('🔧 Demo Mode: Using mock user - no Firebase authentication');
    }
    return Stream.value(UserModel(
      id: 'priyankesh_user',
      name: 'Priyankesh',
      email: '<EMAIL>',
      tier: UserTier.silver,
      points: 750,
      pointsToNextTier: 250,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    ));
  }

  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) async* {
      if (user == null) {
        yield null;
        return;
      }

      try {
        // Check if user profile exists, create if not
        final userDoc = await FirebaseService.firestore
            .collection('users')
            .doc(user.uid)
            .get();

        if (!userDoc.exists) {
          // Create default user profile
          final newUser = UserModel(
            id: user.uid,
            name: user.displayName ?? 'User',
            email: user.email ?? '',
            tier: UserTier.bronze,
            points: 0,
            pointsToNextTier: 500,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await FirebaseService.firestore
              .collection('users')
              .doc(user.uid)
              .set(newUser.toFirestore());
        }

        // Stream user data
        yield* FirebaseService.firestore
            .collection('users')
            .doc(user.uid)
            .snapshots()
            .map((doc) => doc.exists ? UserModel.fromFirestore(doc) : null);
      } catch (e) {
        if (kDebugMode) {
          print('Error in currentUserProvider: $e');
        }
        yield null;
      }
    },
    loading: () => Stream.value(null),
    error: (_, __) => Stream.value(null),
  );
});

// Auth service provider
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService(ref);
});

class AuthService {
  final Ref _ref;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  AuthService(this._ref);

  // Sign in with email and password
  Future<UserCredential?> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    if (kDemoMode) {
      // Reset logged out state on sign in
      _ref.read(_demoLoggedOutProvider.notifier).state = false;
      return await MockAuthService.signInWithEmailAndPassword(email, password);
    }

    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Update last login time
      if (credential.user != null) {
        await _updateLastLogin(credential.user!.uid);
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Create user with email and password
  Future<UserCredential?> createUserWithEmailAndPassword(
    String email,
    String password,
    String name,
  ) async {
    if (kDemoMode) {
      // Reset logged out state on sign up
      _ref.read(_demoLoggedOutProvider.notifier).state = false;
      return await MockAuthService.createUserWithEmailAndPassword(email, password);
    }

    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Update display name
        await credential.user!.updateDisplayName(name);

        // Create user document in Firestore
        final user = UserModel(
          id: credential.user!.uid,
          name: name,
          email: email,
          tier: UserTier.bronze,
          points: 0,
          pointsToNextTier: 500,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await FirebaseService.firestore
            .collection('users')
            .doc(credential.user!.uid)
            .set(user.toFirestore());

        // Send email verification
        await credential.user!.sendEmailVerification();
      }

      return credential;
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Sign out
  Future<void> signOut() async {
    if (kDemoMode) {
      // In demo mode, set the logged out state
      if (kDebugMode) {
        print('🔧 Demo Mode: Signing out mock user');
      }
      _ref.read(_demoLoggedOutProvider.notifier).state = true;
      await MockAuthService.signOut();
      
      // Force refresh of auth state providers
      _ref.invalidate(authStateProvider);
      _ref.invalidate(currentUserProvider);
      return;
    }
    await _auth.signOut();
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    if (kDemoMode) {
      // Mock implementation
      await Future.delayed(const Duration(seconds: 1));
      return;
    }

    try {
      await _auth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Update user profile
  Future<void> updateUserProfile({
    String? displayName,
    String? photoURL,
  }) async {
    if (kDemoMode) return;

    final user = _auth.currentUser;
    if (user != null) {
      await user.updateDisplayName(displayName);
      if (photoURL != null) {
        await user.updatePhotoURL(photoURL);
      }
    }
  }

  // Delete user account
  Future<void> deleteAccount() async {
    if (kDemoMode) return;

    final user = _auth.currentUser;
    if (user != null) {
      // Delete user document from Firestore
      await FirebaseService.firestore
          .collection('users')
          .doc(user.uid)
          .delete();

      // Delete Firebase Auth account
      await user.delete();
    }
  }

  // Re-authenticate user (required for sensitive operations)
  Future<void> reauthenticateUser(String password) async {
    if (kDemoMode) return;

    final user = _auth.currentUser;
    if (user != null && user.email != null) {
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );
      await user.reauthenticateWithCredential(credential);
    }
  }

  // Update last login time - only if not in demo mode
  Future<void> _updateLastLogin(String userId) async {
    if (kDemoMode) return; // Skip Firebase calls in demo mode
    
    try {
      await FirebaseService.firestore
          .collection('users')
          .doc(userId)
          .update({
        'lastLoginAt': DateTime.now(),
        'updatedAt': DateTime.now(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Failed to update last login: $e');
      }
      // Don't rethrow - this is not critical functionality
    }
  }

  // Update user profile in Firestore - only if not in demo mode
  Future<void> updateUserProfileData(UserModel user) async {
    if (kDemoMode) return; // Skip Firebase calls in demo mode
    
    try {
      await FirebaseService.firestore
          .collection('users')
          .doc(user.id)
          .update(user.toFirestore());
    } catch (e) {
      throw Exception('Failed to update profile: $e');
    }
  }

  // Handle Firebase Auth exceptions
  String _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found for that email.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'email-already-in-use':
        return 'The account already exists for that email.';
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Try again later.';
      case 'operation-not-allowed':
        return 'Signing in with Email and Password is not enabled.';
      default:
        return 'An error occurred: ${e.message}';
    }
  }
}

// Auth state notifier for complex auth operations
class AuthNotifier extends StateNotifier<AsyncValue<UserModel?>> {
  AuthNotifier(this._authService, this._ref) : super(const AsyncValue.loading());

  final AuthService _authService;
  final Ref _ref;

  Future<void> signIn(String email, String password) async {
    state = const AsyncValue.loading();
    try {
      await _authService.signInWithEmailAndPassword(email, password);
      // User state will be updated automatically through currentUserProvider
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> signUp(String email, String password, String name) async {
    state = const AsyncValue.loading();
    try {
      await _authService.createUserWithEmailAndPassword(email, password, name);
      // User state will be updated automatically through currentUserProvider
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> signOut() async {
    state = const AsyncValue.loading();
    try {
      if (kDebugMode) {
        print('🔧 Auth Notifier: Signing out user');
      }
      await _authService.signOut();
      state = const AsyncValue.data(null);
      
      // Force refresh of auth state providers
      _ref.invalidate(authStateProvider);
      _ref.invalidate(currentUserProvider);
      
      if (kDebugMode) {
        print('🔧 Auth Notifier: Sign out completed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('🚨 Error during sign out: $e');
      }
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> resetPassword(String email) async {
    try {
      await _authService.resetPassword(email);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

final authNotifierProvider = StateNotifierProvider<AuthNotifier, AsyncValue<UserModel?>>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService, ref);
});
