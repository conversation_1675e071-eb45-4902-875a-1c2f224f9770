import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'firebase_options.dart';
import 'core/services/firebase_service.dart';
import 'app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // DEBUG LOGGING: App initialization
  if (kDebugMode) {
    print('=== EcoCura App Initialization ===');
    print('Platform: ${defaultTargetPlatform.name}');
    print('Is Web: $kIsWeb');
    print('Debug Mode: $kDebugMode');
    print('Demo Mode: $kDemoMode');
  }

  // Initialize Firebase with proper error handling
  try {
    // Load environment variables
    await dotenv.load(fileName: ".env");

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    // Initialize Firebase services
    await FirebaseService.initialize();
    if (kDebugMode) {
      print('✅ Firebase initialized successfully');
      print('✅ Environment variables loaded');
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Initialization failed: $e');
      print('App will continue without Firebase features');
    }
  }

  // Initialization complete - no theme debugging needed for light-only app

  runApp(
    const ProviderScope(
      child: EcoCuraApp(),
    ),
  );
}
