import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/theme/app_theme.dart';
import 'core/navigation/app_router.dart';

// Demo mode flag - set to true to use mock auth and avoid Firestore issues
const bool kDemoMode = false;

class EcoCuraApp extends ConsumerWidget {
  const EcoCuraApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);

    return MaterialApp.router(
      title: 'EcoCura - Recycle Radar',
      theme: AppTheme.lightTheme,
      themeMode: ThemeMode.light, // Force light theme only
      routerConfig: router,
      debugShowCheckedModeBanner: false,
    );
  }
}
