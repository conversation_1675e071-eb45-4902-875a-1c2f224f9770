import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../models/community_post.dart';
import '../services/community_service.dart';
import '../../../shared/providers/auth_provider.dart';

// Community posts stream provider
final communityPostsProvider = StreamProvider<List<CommunityPost>>((ref) {
  return CommunityService.getPosts();
});

// User's posts provider
final userPostsProvider = StreamProvider.family<List<CommunityPost>, String>((ref, userId) {
  return CommunityService.getPostsByUser(userId);
});

// Community post notifier for creating and managing posts
final communityNotifierProvider = StateNotifierProvider<CommunityNotifier, CommunityState>((ref) {
  return CommunityNotifier(ref);
});

class CommunityState {
  final bool isLoading;
  final String? error;
  final List<CommunityPost> posts;

  CommunityState({
    this.isLoading = false,
    this.error,
    this.posts = const [],
  });

  CommunityState copyWith({
    bool? isLoading,
    String? error,
    List<CommunityPost>? posts,
  }) {
    return CommunityState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      posts: posts ?? this.posts,
    );
  }
}

class CommunityNotifier extends StateNotifier<CommunityState> {
  final Ref _ref;

  CommunityNotifier(this._ref) : super(CommunityState());

  /// Create a new community post
  Future<bool> createPost({
    required String content,
    List<String> imageUrls = const [],
    List<String> tags = const [],
    PostType type = PostType.general,
    Map<String, dynamic>? metadata,
  }) async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) {
      state = state.copyWith(error: 'User not authenticated');
      return false;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final post = CommunityPost(
        id: '', // Will be set by Firestore
        authorId: currentUser.id,
        authorName: currentUser.name,
        authorAvatar: currentUser.profileImageUrl,
        content: content,
        imageUrls: imageUrls,
        tags: tags,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        type: type,
        metadata: metadata,
      );

      await CommunityService.createPost(post);

      state = state.copyWith(isLoading: false);

      if (kDebugMode) {
        print('✅ Community post created successfully');
      }

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to create post: $e',
      );

      if (kDebugMode) {
        print('❌ Error creating community post: $e');
      }

      return false;
    }
  }

  /// Like or unlike a post
  Future<void> toggleLike(String postId) async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) return;

    try {
      await CommunityService.toggleLike(postId, currentUser.id);

      if (kDebugMode) {
        print('✅ Post like toggled: $postId');
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to toggle like: $e');

      if (kDebugMode) {
        print('❌ Error toggling like: $e');
      }
    }
  }

  /// Add a comment to a post
  Future<void> addComment(String postId, String content) async {
    final currentUser = _ref.read(currentUserProvider).value;
    if (currentUser == null) return;

    try {
      await CommunityService.addComment(postId, currentUser.id, content);

      if (kDebugMode) {
        print('✅ Comment added to post: $postId');
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to add comment: $e');

      if (kDebugMode) {
        print('❌ Error adding comment: $e');
      }
    }
  }

  /// Delete a post
  Future<void> deletePost(String postId) async {
    try {
      await CommunityService.deletePost(postId);

      if (kDebugMode) {
        print('✅ Post deleted: $postId');
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to delete post: $e');

      if (kDebugMode) {
        print('❌ Error deleting post: $e');
      }
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Comments provider for a specific post
final postCommentsProvider = StreamProvider.family<List<Map<String, dynamic>>, String>((ref, postId) {
  return CommunityService.getComments(postId);
});

// Sample posts for demo/fallback
class SampleCommunityPosts {
  static List<CommunityPost> getSamplePosts() {
    final now = DateTime.now();
    
    return [
      CommunityPost(
        id: 'sample_1',
        authorId: 'demo_user_1',
        authorName: 'Sarah Green',
        content: 'Just finished turning old plastic bottles into beautiful planters! 🌱 The key is to use a heated nail to make drainage holes. Who else loves upcycling plastic waste?',
        imageUrls: ['assets/images/sample_upcycle_1.jpg'],
        tags: ['upcycling', 'plastic', 'planters', 'diy'],
        likes: ['demo_user_2', 'demo_user_3'],
        likeCount: 2,
        commentCount: 5,
        createdAt: now.subtract(const Duration(hours: 2)),
        updatedAt: now.subtract(const Duration(hours: 2)),
        type: PostType.upcycling,
      ),
      CommunityPost(
        id: 'sample_2',
        authorId: 'demo_user_2',
        authorName: 'Mike Eco',
        content: 'Quick tip: Before throwing away cardboard boxes, check if they can be turned into organizers! I made this desk organizer from an old Amazon box. Zero waste! ♻️',
        imageUrls: ['assets/images/sample_organizer.jpg'],
        tags: ['cardboard', 'organizer', 'zerowaste', 'tip'],
        likes: ['demo_user_1'],
        likeCount: 1,
        commentCount: 3,
        createdAt: now.subtract(const Duration(hours: 5)),
        updatedAt: now.subtract(const Duration(hours: 5)),
        type: PostType.tip,
      ),
      CommunityPost(
        id: 'sample_3',
        authorId: 'demo_user_3',
        authorName: 'Emma Craft',
        content: 'Challenge accepted! 🏆 This week I\'m trying to upcycle 5 different items instead of throwing them away. Who wants to join the #UpcycleChallenge?',
        tags: ['challenge', 'upcycle', 'community'],
        likes: ['demo_user_1', 'demo_user_2'],
        likeCount: 2,
        commentCount: 8,
        createdAt: now.subtract(const Duration(days: 1)),
        updatedAt: now.subtract(const Duration(days: 1)),
        type: PostType.challenge,
      ),
    ];
  }
}
