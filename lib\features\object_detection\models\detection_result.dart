/// Represents the result of object detection using TensorFlow Lite
class DetectionResult {
  final String objectName;
  final String materialType;
  final double confidence;
  final bool isRecyclable;
  final List<String> upcyclingIdeas;
  final BoundingBox? boundingBox;

  DetectionResult({
    required this.objectName,
    required this.materialType,
    required this.confidence,
    required this.isRecyclable,
    required this.upcyclingIdeas,
    this.boundingBox,
  });

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() {
    return {
      'objectName': objectName,
      'materialType': materialType,
      'confidence': confidence,
      'isRecyclable': isRecyclable,
      'upcyclingIdeas': upcyclingIdeas,
      'boundingBox': boundingBox?.toJson(),
    };
  }

  /// Create from JSON
  factory DetectionResult.fromJson(Map<String, dynamic> json) {
    return DetectionResult(
      objectName: json['objectName'] ?? '',
      materialType: json['materialType'] ?? '',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      isRecyclable: json['isRecyclable'] ?? false,
      upcyclingIdeas: List<String>.from(json['upcyclingIdeas'] ?? []),
      boundingBox: json['boundingBox'] != null 
          ? BoundingBox.fromJson(json['boundingBox'])
          : null,
    );
  }

  /// Get confidence as percentage
  String get confidencePercentage => '${(confidence * 100).toInt()}%';

  /// Get recyclability status as string
  String get recyclabilityStatus => isRecyclable ? 'Recyclable' : 'Non-recyclable';

  /// Get the primary upcycling suggestion
  String get primaryUpcyclingIdea => upcyclingIdeas.isNotEmpty 
      ? upcyclingIdeas.first 
      : 'Creative project';

  @override
  String toString() {
    return 'DetectionResult(object: $objectName, material: $materialType, confidence: $confidencePercentage)';
  }
}

/// Represents the bounding box coordinates of a detected object
class BoundingBox {
  final double x;
  final double y;
  final double width;
  final double height;

  BoundingBox({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'width': width,
      'height': height,
    };
  }

  /// Create from JSON
  factory BoundingBox.fromJson(Map<String, dynamic> json) {
    return BoundingBox(
      x: (json['x'] ?? 0.0).toDouble(),
      y: (json['y'] ?? 0.0).toDouble(),
      width: (json['width'] ?? 0.0).toDouble(),
      height: (json['height'] ?? 0.0).toDouble(),
    );
  }

  /// Get the center point of the bounding box
  Point get center => Point(x + width / 2, y + height / 2);

  /// Get the area of the bounding box
  double get area => width * height;

  @override
  String toString() {
    return 'BoundingBox(x: $x, y: $y, width: $width, height: $height)';
  }
}

/// Represents a 2D point
class Point {
  final double x;
  final double y;

  Point(this.x, this.y);

  @override
  String toString() => 'Point($x, $y)';
}

/// Detection confidence levels
enum ConfidenceLevel {
  low,
  medium,
  high,
  veryHigh;

  static ConfidenceLevel fromConfidence(double confidence) {
    if (confidence >= 0.9) return ConfidenceLevel.veryHigh;
    if (confidence >= 0.7) return ConfidenceLevel.high;
    if (confidence >= 0.5) return ConfidenceLevel.medium;
    return ConfidenceLevel.low;
  }

  String get displayName {
    switch (this) {
      case ConfidenceLevel.low:
        return 'Low Confidence';
      case ConfidenceLevel.medium:
        return 'Medium Confidence';
      case ConfidenceLevel.high:
        return 'High Confidence';
      case ConfidenceLevel.veryHigh:
        return 'Very High Confidence';
    }
  }
}

/// Material categories for recycling
enum MaterialCategory {
  plastic,
  glass,
  metal,
  paper,
  organic,
  electronic,
  textile,
  wood,
  mixed;

  String get displayName {
    switch (this) {
      case MaterialCategory.plastic:
        return 'Plastic';
      case MaterialCategory.glass:
        return 'Glass';
      case MaterialCategory.metal:
        return 'Metal';
      case MaterialCategory.paper:
        return 'Paper & Cardboard';
      case MaterialCategory.organic:
        return 'Organic Waste';
      case MaterialCategory.electronic:
        return 'Electronics';
      case MaterialCategory.textile:
        return 'Textiles';
      case MaterialCategory.wood:
        return 'Wood';
      case MaterialCategory.mixed:
        return 'Mixed Materials';
    }
  }

  String get recyclingInstructions {
    switch (this) {
      case MaterialCategory.plastic:
        return 'Clean and sort by plastic type. Check recycling symbols.';
      case MaterialCategory.glass:
        return 'Remove caps and lids. Sort by color if required.';
      case MaterialCategory.metal:
        return 'Clean containers. Aluminum and steel are highly recyclable.';
      case MaterialCategory.paper:
        return 'Keep dry and clean. Remove any plastic coatings.';
      case MaterialCategory.organic:
        return 'Compost if possible. Great for garden fertilizer.';
      case MaterialCategory.electronic:
        return 'Take to e-waste recycling center. Contains valuable materials.';
      case MaterialCategory.textile:
        return 'Donate if in good condition, or use for cleaning rags.';
      case MaterialCategory.wood:
        return 'Reuse for projects or compost if untreated.';
      case MaterialCategory.mixed:
        return 'Separate materials when possible for better recycling.';
    }
  }
}
