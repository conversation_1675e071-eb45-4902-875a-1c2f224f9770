rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow reading user profiles for other users (for social features)
      allow read: if request.auth != null;
      
      // User's chat sessions
      match /chat_sessions/{sessionId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
        
        // Chat messages within sessions
        match /messages/{messageId} {
          allow read, write: if request.auth != null && request.auth.uid == userId;
        }
      }
      
      // User's favorites
      match /favorites/{favoriteId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
      }
    }
    
    // Products are readable by all authenticated users
    // Only product owners and admins can write to their products
    match /products/{productId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.sellerId;
      allow update, delete: if request.auth != null && 
        (request.auth.uid == resource.data.sellerId || 
         isAdmin(request.auth.uid));
    }
    
    // Categories are readable by all authenticated users
    // Only admins can write
    match /categories/{categoryId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && isAdmin(request.auth.uid);
    }
    
    // Orders are readable/writable by the order owner and seller
    match /orders/{orderId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.userId || 
         request.auth.uid == resource.data.sellerId);
    }
    
    // Reviews are readable by all, writable by authenticated users
    match /reviews/{reviewId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      allow update, delete: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Community posts are readable by all authenticated users
    match /community_posts/{postId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.authorId;
      allow update, delete: if request.auth != null && 
        (request.auth.uid == resource.data.authorId || 
         isAdmin(request.auth.uid));
         
      // Comments on posts
      match /comments/{commentId} {
        allow read: if request.auth != null;
        allow create: if request.auth != null && 
          request.auth.uid == request.resource.data.authorId;
        allow update, delete: if request.auth != null && 
          request.auth.uid == resource.data.authorId;
      }
    }
    
    // Upcycling projects
    match /upcycling_projects/{projectId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.authorId;
      allow update, delete: if request.auth != null && 
        (request.auth.uid == resource.data.authorId || 
         isAdmin(request.auth.uid));
    }
    
    // App configuration (admin only)
    match /config/{configId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && isAdmin(request.auth.uid);
    }
    
    // Admin collection (admin only)
    match /admin/{document=**} {
      allow read, write: if request.auth != null && isAdmin(request.auth.uid);
    }
    
    // Helper function to check if user is admin
    function isAdmin(userId) {
      return userId in [
        '<EMAIL>',
        '<EMAIL>'
      ] || exists(/databases/$(database)/documents/admin/users/$(userId));
    }
    
    // Allow read access to public collections for demo purposes
    // Remove this in production
    match /{document=**} {
      allow read: if request.auth != null;
    }
  }
}
