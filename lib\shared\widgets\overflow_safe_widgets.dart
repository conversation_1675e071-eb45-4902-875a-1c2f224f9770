import 'package:flutter/material.dart';

/// A collection of overflow-safe widgets to prevent UI layout issues
class OverflowSafeWidgets {
  
  /// Safe Row that prevents overflow by making children flexible
  static Widget safeRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
    EdgeInsets? padding,
  }) {
    return Container(
      padding: padding,
      child: Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        children: children.map((child) {
          // Wrap text widgets in Flexible to prevent overflow
          if (child is Text) {
            return Flexible(child: child);
          }
          return child;
        }).toList(),
      ),
    );
  }

  /// Safe Text that automatically handles overflow
  static Widget safeText(
    String text, {
    TextStyle? style,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    TextAlign? textAlign,
  }) {
    return Text(
      text,
      style: style,
      maxLines: maxLines,
      overflow: overflow,
      textAlign: textAlign,
      softWrap: true,
    );
  }

  /// Safe Column that prevents overflow with scrolling
  static Widget safeColumn({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    MainAxisSize mainAxisSize = MainAxisSize.max,
    EdgeInsets? padding,
    bool scrollable = false,
  }) {
    final column = Column(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children,
    );

    if (scrollable) {
      return SingleChildScrollView(
        padding: padding,
        child: column,
      );
    }

    return Container(
      padding: padding,
      child: column,
    );
  }

  /// Safe Container with responsive constraints
  static Widget safeContainer({
    required Widget child,
    EdgeInsets? padding,
    EdgeInsets? margin,
    BoxDecoration? decoration,
    double? width,
    double? height,
    double? maxWidth,
    double? maxHeight,
  }) {
    return Container(
      padding: padding,
      margin: margin,
      decoration: decoration,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? double.infinity,
        maxHeight: maxHeight ?? double.infinity,
        minWidth: 0,
        minHeight: 0,
      ),
      width: width,
      height: height,
      child: child,
    );
  }

  /// Safe ListView builder with automatic shrinkWrap
  static Widget safeListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    EdgeInsets? padding,
    bool shrinkWrap = true,
    ScrollPhysics? physics,
    Axis scrollDirection = Axis.vertical,
    Widget Function(BuildContext, int)? separatorBuilder,
  }) {
    if (separatorBuilder != null) {
      return ListView.separated(
        itemCount: itemCount,
        itemBuilder: itemBuilder,
        separatorBuilder: separatorBuilder,
        padding: padding,
        shrinkWrap: shrinkWrap,
        physics: physics ?? (shrinkWrap ? const NeverScrollableScrollPhysics() : null),
        scrollDirection: scrollDirection,
      );
    }

    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics ?? (shrinkWrap ? const NeverScrollableScrollPhysics() : null),
      scrollDirection: scrollDirection,
    );
  }

  /// Safe GridView with automatic responsive sizing
  static Widget safeGridView({
    required List<Widget> children,
    int crossAxisCount = 2,
    double crossAxisSpacing = 8.0,
    double mainAxisSpacing = 8.0,
    double childAspectRatio = 1.0,
    EdgeInsets? padding,
    bool shrinkWrap = true,
    ScrollPhysics? physics,
  }) {
    return GridView.count(
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      childAspectRatio: childAspectRatio,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics ?? (shrinkWrap ? const NeverScrollableScrollPhysics() : null),
      children: children,
    );
  }

  /// Safe Wrap widget for handling multiple children
  static Widget safeWrap({
    required List<Widget> children,
    Axis direction = Axis.horizontal,
    WrapAlignment alignment = WrapAlignment.start,
    double spacing = 0.0,
    WrapAlignment runAlignment = WrapAlignment.start,
    double runSpacing = 0.0,
    WrapCrossAlignment crossAxisAlignment = WrapCrossAlignment.start,
  }) {
    return Wrap(
      direction: direction,
      alignment: alignment,
      spacing: spacing,
      runAlignment: runAlignment,
      runSpacing: runSpacing,
      crossAxisAlignment: crossAxisAlignment,
      children: children,
    );
  }

  /// Safe flexible content that adapts to available space
  static Widget flexibleContent({
    required Widget child,
    int flex = 1,
    FlexFit fit = FlexFit.loose,
  }) {
    return Flexible(
      flex: flex,
      fit: fit,
      child: child,
    );
  }

  /// Safe expanded content that takes all available space
  static Widget expandedContent({
    required Widget child,
    int flex = 1,
  }) {
    return Expanded(
      flex: flex,
      child: child,
    );
  }

  /// Safe card with responsive constraints
  static Widget safeCard({
    required Widget child,
    EdgeInsets? margin,
    EdgeInsets? padding,
    double? elevation,
    Color? color,
    ShapeBorder? shape,
    Clip? clipBehavior,
  }) {
    return Card(
      margin: margin ?? const EdgeInsets.all(8.0),
      elevation: elevation,
      color: color,
      shape: shape,
      clipBehavior: clipBehavior,
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  /// Responsive padding that adapts to screen size
  static EdgeInsets responsivePadding(BuildContext context, {
    double small = 8.0,
    double medium = 16.0,
    double large = 24.0,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return EdgeInsets.all(small);
    } else if (screenWidth < 900) {
      return EdgeInsets.all(medium);
    } else {
      return EdgeInsets.all(large);
    }
  }

  /// Responsive spacing that adapts to screen size
  static double responsiveSpacing(BuildContext context, {
    double small = 8.0,
    double medium = 16.0,
    double large = 24.0,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 600) {
      return small;
    } else if (screenWidth < 900) {
      return medium;
    } else {
      return large;
    }
  }
}

/// Extension methods for existing widgets to make them overflow-safe
extension WidgetOverflowSafetyExtensions on Widget {
  /// Make any widget flexible
  Widget get flexible => Flexible(child: this);
  
  /// Make any widget expanded
  Widget get expanded => Expanded(child: this);
  
  /// Wrap widget in safe container
  Widget safeContainer({
    EdgeInsets? padding,
    EdgeInsets? margin,
    BoxDecoration? decoration,
  }) => OverflowSafeWidgets.safeContainer(
    child: this,
    padding: padding,
    margin: margin,
    decoration: decoration,
  );
}
