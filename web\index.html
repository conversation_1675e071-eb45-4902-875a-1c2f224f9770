<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="EcoCura - Sustainable Upcycling Companion">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="EcoCura">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>EcoCura - Sustainable Upcycling</title>
  <link rel="manifest" href="manifest.json">

  <!-- Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-storage-compat.js"></script>
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
  
  <!-- Firebase Configuration -->
  <script type="module">
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.0/firebase-app.js';
    import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.0/firebase-auth.js';
    import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore.js';
    import { getStorage } from 'https://www.gstatic.com/firebasejs/10.7.0/firebase-storage.js';
    import { getAnalytics } from 'https://www.gstatic.com/firebasejs/10.7.0/firebase-analytics.js';

    const firebaseConfig = {
      apiKey: "AIzaSyDstBgDfTCSBgrR1Js3Lm2AKUsWiAirLyU",
      authDomain: "ecocura-e5ddd.firebaseapp.com",
      projectId: "ecocura-e5ddd",
      storageBucket: "ecocura-e5ddd.firebasestorage.app",
      messagingSenderId: "192005607529",
      appId: "1:192005607529:web:b2b154cb1bfed8539492f8",
      measurementId: "G-RF6HSQ64CQ"
    };

    const app = initializeApp(firebaseConfig);
    const analytics = getAnalytics(app);
    
    window.firebase = {
      app,
      auth: getAuth(app),
      firestore: getFirestore(app),
      storage: getStorage(app),
      analytics
    };
  </script>
</body>
</html>
